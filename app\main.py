"""
main.py
FastAPI 服务入口
- 定义 HTTP 接口
- 调用 predict.py 完成预测
"""
from typing import List, Optional
from fastapi import FastAPI
from pydantic import BaseModel, Field
from .predict import predict 

app = FastAPI(title="Mouse-carcinogenicity-model")  # 大鼠致癌性预测模型
# 🔧 需要修改：将 "Rat-carcinogenicity-model" 替换为对应的模型名称

# ==== 请求 & 响应模型 ====
class PredictionRequest(BaseModel):
    smiles_list: List[str]  # 要预测的 SMILES 列表

class SingleResult(BaseModel):
    """
    完整的预测结果模型
    """
    smiles: str
    canonical_smiles: Optional[str] = None
    prediction: Optional[int] = None
    probability: Optional[float] = None
    prediction_label: Optional[str] = None
    error: Optional[str] = None
    # 应用域原始计算字段（带竖线 | 的字段需替换为下划线 _）
    simiDensity_exp: Optional[float] = Field(None, alias="simiDensity|exp")
    simiWtDegee_w_exp: Optional[float] = Field(None, alias="simiWtDegee_w|exp")
    simiWtLD_w_exp: Optional[float] = Field(None, alias="simiWtLD_w|exp")
    # 应用域处理后字段
    in_applicability_domain: Optional[bool] = None
    ad_densLB_threshold: Optional[float] = None
    ad_LdUB_threshold: Optional[float] = None
    ad_density_value: Optional[float] = None
    ad_ld_value: Optional[float] = None
    ad_reason: Optional[str] = None

# ==== API 路由 ====
@app.post("/predict", response_model=List[SingleResult])
async def batch_predict(request: PredictionRequest):
    """
    批量预测接口
    输入：SMILES 列表
    输出：预测结果 + 应用域信息（如果可用）
    """
    print(f"收到预测请求: {request}")
    print(f"SMILES列表: {request.smiles_list}")

    df = predict(request.smiles_list)  # 调用 predict.py
    results = df.to_dict(orient="records")

    print(f"预测结果: {results}")
    return [SingleResult(**row).dict(by_alias=True) for row in results]

# 添加一个简单的测试接口
@app.post("/test")
async def test_endpoint(data: dict):
    """简单测试接口，用于调试"""
    print(f"收到测试数据: {data}")
    return {"received": data, "status": "ok"}

@app.get("/health")
def health_check():
    """
    健康检查接口
    """
    return {"status": "ok"}