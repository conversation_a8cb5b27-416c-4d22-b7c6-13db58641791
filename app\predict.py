import os
import sys
import argparse
import pandas as pd
import numpy as np
import pickle
import joblib
from rdkit import Chem
from rdkit.Chem import Descriptors, rdMolDescriptors
from rdkit.Chem.rdMolDescriptors import GetMACCSKeysFingerprint
from rdkit.Avalon import pyAvalonTools


# 基于训练集的应用域检查器
class TrainingSetApplicabilityDomainChecker:
    """基于训练集数据的应用域检查器"""

    def __init__(self, task_name):
        self.task_name = task_name
        self.similarity_threshold = 0.001  # 相似性阈值（更宽松）
        self.roughness_threshold = 0.55    # 局域崎岖性阈值（更宽松）
        self.training_smiles = []
        self.training_fps = []
        self.training_labels = []  # 存储训练集标签用于局域崎岖性计算
        self._load_training_data()

    def _load_training_data(self):
        """加载训练集数据"""
        try:
            # 加载Excel文件
            data_file = os.path.join(MODEL_DIR, 'Mouse_clean_MACCS_XGB_predictions.xlsx')
            if os.path.exists(data_file):
                df = pd.read_excel(data_file)

                # 筛选训练集数据
                if 'set' in df.columns and 'SMILES' in df.columns:
                    train_data = df[df['set'] == 'train']
                    self.training_smiles = train_data['SMILES'].tolist()

                    # 获取训练集标签（假设标签列名为 'label' 或 'class' 或类似）
                    label_columns = ['label', 'class', 'target', 'y', 'activity', 'Result_new']
                    label_col = None
                    for col in label_columns:
                        if col in train_data.columns:
                            label_col = col
                            break

                    if label_col:
                        self.training_labels = train_data[label_col].tolist()
                        print(f"Successfully loaded training labels from column: {label_col}")
                        print(f"Label distribution: {train_data[label_col].value_counts().to_dict()}")
                    else:
                        # 如果找不到标签列，使用默认值
                        self.training_labels = [0] * len(self.training_smiles)
                        print("Warning: No label column found, using default labels")
                        print(f"Available columns: {train_data.columns.tolist()}")

                    # 预计算训练集的分子指纹
                    self._precompute_fingerprints()
                    print(f"Loaded {len(self.training_smiles)} training compounds for AD checking")
                else:
                    print("Warning: Required columns (set, SMILES) not found in data file")
            else:
                print(f"Warning: Training data file not found: {data_file}")
        except Exception as e:
            print(f"Error loading training data: {e}")

    def _precompute_fingerprints(self):
        """预计算训练集分子指纹"""
        from rdkit import Chem
        from rdkit.Avalon import pyAvalonTools

        self.training_fps = []
        for smiles in self.training_smiles[:500]:  # 限制数量以提高性能
            try:
                mol = Chem.MolFromSmiles(smiles)
                if mol is not None:
                    fp = pyAvalonTools.GetAvalonFP(mol, 2048)
                    self.training_fps.append(fp)
            except:
                continue

        print(f"Precomputed {len(self.training_fps)} fingerprints for AD checking")

    def check_batch(self, smiles_list):
        """批量检查应用域状态"""
        from rdkit import Chem
        from rdkit.Chem import DataStructs
        from rdkit.Avalon import pyAvalonTools

        results = []
        for smiles in smiles_list:
            try:
                # 计算查询分子的指纹
                query_mol = Chem.MolFromSmiles(smiles)
                if query_mol is None:
                    result = {
                        'status': 'Error',
                        'similarity_score': 0.0,
                        'reason': 'Invalid SMILES'
                    }
                else:
                    query_fp = pyAvalonTools.GetAvalonFP(query_mol, 2048)

                    # 计算与训练集的相似性
                    max_similarity = 0.0
                    similarities = []
                    nearest_neighbors = []

                    if self.training_fps:
                        for i, train_fp in enumerate(self.training_fps):
                            sim = DataStructs.TanimotoSimilarity(query_fp, train_fp)
                            similarities.append(sim)
                            if sim >= self.similarity_threshold:
                                nearest_neighbors.append({
                                    'similarity': sim,
                                    'label': self.training_labels[i] if i < len(self.training_labels) else 0,
                                    'distance': 1 - sim  # 距离 = 1 - 相似性
                                })

                        max_similarity = max(similarities) if similarities else 0.0

                        # 计算局域崎岖性（Local Roughness）
                        local_roughness = 0.0
                        if len(nearest_neighbors) >= 2:
                            # 找到最近的邻居
                            nearest_neighbors.sort(key=lambda x: x['distance'])
                            close_neighbors = [n for n in nearest_neighbors if n['distance'] <= self.roughness_threshold]

                            if len(close_neighbors) >= 2:
                                # 计算近邻中标签的方差作为局域崎岖性的度量
                                labels = [n['label'] for n in close_neighbors]
                                if len(set(labels)) > 1:  # 如果标签不一致
                                    local_roughness = np.var(labels)
                                else:
                                    local_roughness = 0.0

                    # 应用域判断
                    similarity_check = max_similarity >= self.similarity_threshold
                    roughness_check = local_roughness <= self.roughness_threshold  # 崎岖性越小越好

                    if similarity_check and roughness_check:
                        status = 'In-Domain'
                        reason = 'In AD'
                    else:
                        status = 'Out-of-Domain'
                        reason = 'Out of AD'

                    result = {
                        'status': status,
                        'similarity_score': max_similarity,
                        'local_roughness': local_roughness,
                        'similarity_threshold': self.similarity_threshold,
                        'roughness_threshold': self.roughness_threshold,
                        'neighbor_count': len(nearest_neighbors),
                        'reason': reason
                    }

            except Exception as e:
                result = {
                    'status': 'Error',
                    'similarity_score': 0.0,
                    'reason': f'AD check failed: {str(e)}'
                }

            results.append(result)

        return pd.DataFrame(results)

# 添加上级目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 配置
DEFAULT_RESULTS_DIR = 'results'
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
MODEL_DIR = os.path.join(SCRIPT_DIR, 'save_model')

print(f"Model directory: {MODEL_DIR}")
if not os.path.exists(MODEL_DIR):
    print(f"Warning: Model directory does not exist: {MODEL_DIR}")
    print("Attempting to create model directory...")
    try:
        os.makedirs(MODEL_DIR, exist_ok=True)
        print(f"Created model directory: {MODEL_DIR}")
    except Exception as e:
        print(f"Failed to create model directory: {e}")

# 🔧 需要修改：模型配置注释和ENDPOINTS字典
# 模型配置 - 小鼠致癌性预测模型
ENDPOINTS = {
    'Mouse_clean_MACCS_XGB': 'MACCS'
}

class XGBoostPredictor:
    def __init__(self):
        self.models = {}
        self.model_infos = {}
        self.ad_checkers = {}
        self.loaded_models_count = self._load_models()
        self._initialize_ad_checkers()
        
        if self.loaded_models_count == 0:
            print("Warning: No models loaded successfully!")
            print("Please ensure model files are placed in the correct location!")
            print(f"Models should be located at: {MODEL_DIR}")
            print("Model files should be named: Mouse_clean_MACCS_XGB_best_model.joblib etc")
        else:
            print(f"Successfully loaded {self.loaded_models_count} model(s)")

    def _load_models(self):
        """加载所有XGBoost模型"""
        print("Loading XGBoost models...")
        loaded_count = 0
        
        for endpoint, feature_type in ENDPOINTS.items():
            model_path = os.path.join(MODEL_DIR, f'{endpoint}_best_model.joblib')
            info_path = os.path.join(MODEL_DIR, f'{endpoint}_model_info.pkl')
            
            # 检查模型文件是否存在
            if not os.path.exists(model_path):
                print(f"Model file not found: {model_path}")
                continue

            try:
                # 加载模型 - 使用joblib
                import joblib
                model = joblib.load(model_path)
                self.models[endpoint] = model

                # 加载模型信息（包含scaler）
                scaler = None
                if os.path.exists(info_path):
                    with open(info_path, 'rb') as f:
                        model_info = pickle.load(f)
                        if 'scaler' in model_info:
                            scaler = model_info['scaler']

                # 保存模型信息
                self.model_infos[endpoint] = {
                    'feature_type': feature_type,
                    'scaler': scaler
                }

                print(f"Loaded {endpoint}")
                loaded_count += 1

            except Exception as e:
                print(f"Failed to load {endpoint}: {e}")
        
        return loaded_count

    def _initialize_ad_checkers(self):
        """初始化AD检查器"""
        for endpoint in self.models.keys():
            try:
                task_name = f"{endpoint}_predictions"
                ad_checker = TrainingSetApplicabilityDomainChecker(task_name)
                self.ad_checkers[endpoint] = ad_checker
                print(f"AD checker initialized for {endpoint}")
            except Exception as e:
                print(f"AD checker failed for {endpoint}: {e}")
                self.ad_checkers[endpoint] = None

    def _extract_features(self, smiles, feature_type):
        """提取分子特征"""
        mol = Chem.MolFromSmiles(smiles)
        if mol is None:
            raise ValueError(f"Invalid SMILES: {smiles}")
        
        if feature_type == 'MACCS':
            fp = GetMACCSKeysFingerprint(mol)
            return np.array(fp)
        elif feature_type == 'Avalon':
            fp = pyAvalonTools.GetAvalonFP(mol, 2048) 
            return np.array(fp)
        else:
            raise ValueError(f"Unknown feature type: {feature_type}")

    def predict_single(self, smiles):
        """预测单个SMILES"""
        results = {'SMILES': smiles}
        
        if len(self.models) == 0:
            results['error'] = "没有加载任何模型，无法进行预测"
            return results
        
        for endpoint in self.models:
            try:
                # 获取特征类型
                feature_type = self.model_infos[endpoint]['feature_type']
                
                # 提取特征
                features = self._extract_features(smiles, feature_type)

                # 验证特征维度
                expected_features = self.model_infos[endpoint].get('n_features', len(features))
                if len(features) != expected_features:
                    print(f"Warning: Feature dimension mismatch for {endpoint}. Expected: {expected_features}, Got: {len(features)}")
                features = features.reshape(1, -1)
                
                # 标准化
                if 'scaler' in self.model_infos[endpoint] and self.model_infos[endpoint]['scaler'] is not None:
                    features = self.model_infos[endpoint]['scaler'].transform(features)
                
                # 预测
                model = self.models[endpoint]
                prediction = model.predict(features)[0]
                probabilities = model.predict_proba(features)[0]
                
                # AD检查 - 获取详细信息
                ad_info = {
                    'status': "Unknown",
                    'similarity_score': None,
                    'local_roughness': None,
                    'similarity_threshold': None,
                    'roughness_threshold': None,
                    'neighbor_count': None,
                    'reason': None
                }

                if self.ad_checkers[endpoint]:
                    try:
                        ad_result = self.ad_checkers[endpoint].check_batch([smiles])

                        if len(ad_result) > 0:
                            # 获取第一行数据
                            first_row = ad_result.iloc[0] if hasattr(ad_result, 'iloc') else ad_result[0]

                            ad_info['status'] = first_row['status'] if 'status' in first_row else 'Unknown'
                            ad_info['similarity_score'] = first_row['similarity_score'] if 'similarity_score' in first_row else None
                            ad_info['local_roughness'] = first_row['local_roughness'] if 'local_roughness' in first_row else None
                            ad_info['similarity_threshold'] = first_row['similarity_threshold'] if 'similarity_threshold' in first_row else None
                            ad_info['roughness_threshold'] = first_row['roughness_threshold'] if 'roughness_threshold' in first_row else None
                            ad_info['neighbor_count'] = first_row['neighbor_count'] if 'neighbor_count' in first_row else None
                            ad_info['reason'] = first_row['reason'] if 'reason' in first_row else None
                    except Exception as e:
                        ad_info['status'] = f"Check Failed: {str(e)}"
                        ad_info['reason'] = str(e)

                results[endpoint] = {
                    'predicted_class': int(prediction),
                    'probabilities': {
                        'class_0': round(float(probabilities[0]), 4),
                        'class_1': round(float(probabilities[1]), 4)
                    },
                    'applicability_domain': ad_info
                }
            
            except Exception as e:
                results[endpoint] = {
                    'error': str(e)
                }
        
        return results

def main():
    # ==================== 在这里修改预测的化合物 ====================
    # 直接修改下面的SMILES列表，然后运行程序即可
    COMPOUNDS_TO_PREDICT = [
        # ("O=C=NCCCCCn1c(=O)n(CCCCCN=C=O)c(=O)n(CCCCCN=C=O)c1=O", "三聚体"),
        # ("O=C=NCCCCCn1c(=O)n(CCCCCN=C=O)c(=O)n(CCCCCn2c(=O)n(CCCCCN=C=O)c(=O)n(CCCCCN=C=O)c2=O)c1=O", "五聚体"),
        # ("O=C=NCCCCCn1c(=O)n(CCCCCN=C=O)c(=O)n(CCCCCn2c(=O)n(CCCCCN=C=O)c(=O)n(CCCCCn3c(=O)n(CCCCCN=C=O)c(=O)n(CCCCCN=C=O)c3=O)c2=O)c1=O", "七聚体"),
        # ("O=C=NCCCCCn1c(=O)n(CCCCCN=C=O)c(=O)n(CCCCCn2c(=O)n(CCCCCN=C=O)c(=O)n(CCCCCn3c(=O)n(CCCCCN=C=O)c(=O)n(CCCCCn4c(=O)n(CCCCCN=C=O)c(=O)n(CCCCCN=C=O)c4=O)c3=O)c2=O)c1=O", "九聚体"),
        # ("O=C=NCCCCCn1c(=O)n(CCCCCN=C=O)c(=O)n(CCCCCn2c(=O)n(CCCCCN=C=O)c(=O)n(CCCCCn3c(=O)n(CCCCCN=C=O)c(=O)n(CCCCCn4c(=O)n(CCCCCN=C=O)c(=O)n(CCCCCn5c(=O)n(CCCCCN=C=O)c(=O)n(CCCCCN=C=O)c5=O)c4=O)c3=O)c2=O)c1=O", "十一聚体"),
    ]
    # ================================================================
    
    parser = argparse.ArgumentParser(description='XGBoost Multi-Endpoint Prediction with AD Check')
    parser.add_argument('--smiles', type=str, help='Single SMILES string for prediction')
    parser.add_argument('--input_csv', type=str, help='Path to input CSV file')
    parser.add_argument('--smiles_column', type=str, default='SMILES', help='Name of SMILES column in CSV')
    parser.add_argument('--output_dir', type=str, default=DEFAULT_RESULTS_DIR, help='Output directory')
    
    args = parser.parse_args()
    
    # Initialize predictor
    predictor = XGBoostPredictor()
    
    # 如果没有提供任何参数，直接预测上面定义的化合物
    if not any([args.smiles, args.input_csv]):
        print("\nStarting prediction for specified compounds")
        print("="*100)

        if predictor.loaded_models_count == 0:
            print("\nError: No models loaded, cannot perform prediction")
            print("Please ensure model files are placed in the correct location!")
            print(f"Models should be located at: {MODEL_DIR}")
            return
            
        for smiles, name in COMPOUNDS_TO_PREDICT:
            print(f"\n化合物: {name}")
            print(f"SMILES: {smiles}")
            print("-" * 100)
            
            try:
                result = predictor.predict_single(smiles)
                
                if 'error' in result:
                    print(f"错误: {result['error']}")
                    continue
                
                # 表格头部
                print(f"{'模型':<30} {'预测结果':<8} {'非致癌概率':<10} {'致癌概率':<10} {'置信度':<10} {'应用域':<8}")
                print("-" * 90)
                
                for endpoint, data in result.items():
                    if endpoint == 'SMILES':
                        continue
                        
                    if 'error' in data:
                        print(f"{endpoint:<35} {'错误':<8} {'-':<10} {'-':<10} {'-':<12} {data['error']:<8}")
                    else:
                        pred_class = data['predicted_class']
                        prob_0 = data['probabilities']['class_0']
                        prob_1 = data['probabilities']['class_1']
                        ad_status = data['applicability_domain']['status']
                        
                        confidence = max(prob_0, prob_1)
                        confidence_level = "高" if confidence > 0.8 else "中" if confidence > 0.6 else "低"
                        pred_result = "非致癌" if pred_class == 0 else "致癌"
                        
                        # 简化应用域状态显示
                        if ad_status == "In-Domain":
                            ad_display = "域内"
                        elif ad_status == "Out-of-Domain":
                            ad_display = "域外"
                        else:
                            ad_display = "错误"
                        
                        # 格式化置信度显示
                        confidence_display = f"{confidence_level}({confidence:.3f})"
                        
                        print(f"{endpoint:<35} {pred_result:<8} {prob_0:<10.4f} {prob_1:<10.4f} {confidence_display:<12} {ad_display:<8}")
                        
            except Exception as e:
                print(f"预测失败: {e}")
        
        print(f"\n{'='*100}")
        print("预测完成!")
        return
    
    elif args.smiles:
        # Single SMILES prediction
        print(f"\n预测化合物: {args.smiles}")
        print("="*100)
        
        result = predictor.predict_single(args.smiles)
        
        # 表格头部
        print(f"{'模型':<30} {'预测结果':<8} {'非致癌概率':<10} {'致癌概率':<10} {'置信度':<10} {'应用域':<8}")
        print("-" * 90)
        
        for endpoint, data in result.items():
            if endpoint == 'SMILES':
                continue
                
            if 'error' in data:
                print(f"{endpoint:<35} {'错误':<8} {'-':<10} {'-':<10} {'-':<12} {data['error']:<8}")
            else:
                pred_class = data['predicted_class']
                prob_0 = data['probabilities']['class_0']
                prob_1 = data['probabilities']['class_1']
                ad_status = data['applicability_domain']['status']
                
                confidence = max(prob_0, prob_1)
                confidence_level = "高" if confidence > 0.8 else "中" if confidence > 0.6 else "低"
                pred_result = "非致癌" if pred_class == 0 else "致癌"
                
                # 简化应用域状态显示
                if ad_status == "In-Domain":
                    ad_display = "域内"
                elif ad_status == "Out-of-Domain":
                    ad_display = "域外"
                else:
                    ad_display = "错误"
                
                # 格式化置信度显示
                confidence_display = f"{confidence_level}({confidence:.3f})"
                
                print(f"{endpoint:<35} {pred_result:<8} {prob_0:<10.4f} {prob_1:<10.4f} {confidence_display:<12} {ad_display:<8}")
    
    elif args.input_csv:
        # Batch CSV prediction - simplified version
        print(f"Processing CSV file: {args.input_csv}")
        df = pd.read_csv(args.input_csv)
        
        if args.smiles_column not in df.columns:
            raise ValueError(f"SMILES column '{args.smiles_column}' not found in the CSV file.")

        results_list = []
        for index, row in df.iterrows():
            smiles = row[args.smiles_column]
            result = predictor.predict_single(smiles)
            
            # 简化结果格式
            row_result = {'SMILES': smiles}
            for endpoint, data in result.items():
                if endpoint == 'SMILES':
                    continue
                if 'error' not in data:
                    row_result[f'{endpoint}_prediction'] = data['predicted_class']
                    row_result[f'{endpoint}_prob_carcinogenic'] = data['probabilities']['class_1']
                    row_result[f'{endpoint}_AD_status'] = data['applicability_domain']['status']
            
            results_list.append(row_result)
        
        results_df = pd.DataFrame(results_list)
        
        # Save results
        os.makedirs(args.output_dir, exist_ok=True)
        output_file = os.path.join(args.output_dir, 'predictions.csv')
        results_df.to_csv(output_file, index=False)
        print(f"Results saved to: {output_file}")

# ==== 教程要求的批量预测函数 ====
def predict(smiles_list):
    """
    批量预测函数 - 教程要求的接口

    参数:
        smiles_list: SMILES 字符串列表

    返回:
        pd.DataFrame: 包含预测结果的数据框
    """
    print(f"开始批量预测，共 {len(smiles_list)} 个分子")

    # 处理空列表
    if not smiles_list:
        print("警告：输入的SMILES列表为空")
        return pd.DataFrame()

    # 创建预测器实例
    predictor = XGBoostPredictor()

    # 批量预测
    results = []
    for i, smiles in enumerate(smiles_list):
        print(f"正在预测第 {i+1}/{len(smiles_list)} 个分子: {smiles}")

        try:
            # 调用单个预测
            result = predictor.predict_single(smiles)
            print(f"原始预测结果: {result}")

            # 转换结果格式以匹配 SingleResult 模型
            if isinstance(result, dict):
                # 检查是否有全局错误
                if 'error' in result:
                    formatted_result = {
                        'smiles': smiles,
                        'error': result['error']
                    }
                else:
                    # 找到第一个非SMILES的endpoint
                    endpoint_keys = [k for k in result.keys() if k != 'SMILES']

                    if endpoint_keys:
                        endpoint_key = endpoint_keys[0]
                        endpoint_data = result[endpoint_key]

                        if isinstance(endpoint_data, dict) and 'error' not in endpoint_data:
                            # 获取应用域信息
                            ad_info = endpoint_data.get('applicability_domain', {})
                            ad_status = ad_info.get('status', 'Unknown')
                            similarity_score = ad_info.get('similarity_score', 0.0)
                            local_roughness = ad_info.get('local_roughness', 0.0)
                            similarity_threshold = ad_info.get('similarity_threshold', 0.75)
                            roughness_threshold = ad_info.get('roughness_threshold', 0.05)
                            neighbor_count = ad_info.get('neighbor_count', 0)

                            # 判断是否在应用域内
                            in_ad = ad_status in ['In AD', 'In-Domain', 'In Domain']

                            formatted_result = {
                                'smiles': smiles,
                                'canonical_smiles': smiles,
                                'prediction': endpoint_data.get('predicted_class'),
                                'probability': endpoint_data.get('probabilities', {}).get('class_1'),
                                'prediction_label': '致癌' if endpoint_data.get('predicted_class') == 1 else '非致癌',
                                'error': None,
                                'simiDensity|exp': round(similarity_score, 4) if similarity_score is not None else None,
                                'simiWtDegee_w|exp': round(local_roughness, 4) if local_roughness is not None else None,
                                'simiWtLD_w|exp': neighbor_count,
                                'in_applicability_domain': in_ad,
                                'ad_densLB_threshold': round(roughness_threshold, 4) if roughness_threshold is not None else None,
                                'ad_LdUB_threshold': round(similarity_threshold, 4) if similarity_threshold is not None else None,
                                'ad_density_value': round(similarity_score, 4) if similarity_score is not None else None,
                                'ad_ld_value': round(local_roughness, 4) if local_roughness is not None else None,
                                'ad_reason': 'In AD' if in_ad else 'Out of AD'
                            }
                        else:
                            # endpoint有错误
                            error_msg = endpoint_data.get('error', '未知错误') if isinstance(endpoint_data, dict) else str(endpoint_data)
                            formatted_result = {
                                'smiles': smiles,
                                'error': f'预测失败：{error_msg}'
                            }
                    else:
                        formatted_result = {
                            'smiles': smiles,
                            'error': '预测失败：没有找到有效的预测结果'
                        }

                results.append(formatted_result)
            else:
                # 如果预测失败，创建错误记录
                results.append({
                    'smiles': smiles,
                    'error': f'预测失败：返回结果格式错误，类型为{type(result)}'
                })

        except Exception as e:
            print(f"预测分子 {smiles} 时发生错误: {str(e)}")
            # 创建错误记录
            results.append({
                'smiles': smiles,
                'error': f'预测失败：{str(e)}'
            })

    # 转换为 DataFrame
    df = pd.DataFrame(results)
    print(f"批量预测完成，成功处理 {len(df)} 个结果")

    return df

if __name__ == '__main__':
    main()
